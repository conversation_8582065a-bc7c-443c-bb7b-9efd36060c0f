/**
 * Lien Companion → WinMedia
 * Transmet les commandes de Companion vers WinMedia
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, IControlModule } from '../interfaces/module.interface';
import { IWinmediaModule } from '../../winmedia/winmedia.types';

export class CompanionToWinmediaLink implements IModuleLink {
    public readonly name = 'companion-to-winmedia';
    public readonly description = 'Transmet les commandes Companion vers WinMedia';
    public readonly enabled: boolean;

    private companionModule?: IControlModule;
    private winmediaModule?: IWinmediaModule;
    private cleanupCallbacks: Array<() => void> = [];

    constructor(private config: LinkConfig) {
        this.enabled = config.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        if (!this.enabled) {
            return;
        }

        // Récupérer les modules nécessaires
        this.companionModule = modules.get('companion') as IControlModule;
        this.winmediaModule = modules.get('winmedia') as IWinmediaModule;

        if (!this.companionModule) {
            console.log(`[${this.name}] Companion module not available - link disabled`);
            return;
        }

        if (!this.winmediaModule) {
            console.log(`[${this.name}] WinMedia module not available - link disabled`);
            return;
        }

        console.log(`[${this.name}] Initializing link between Companion and WinMedia`);

        // S'abonner aux événements de Companion
        this.setupCompanionListeners();

        console.log(`[${this.name}] Link initialized successfully`);
    }

    async cleanup(): Promise<void> {
        console.log(`[${this.name}] Cleaning up link...`);
        
        // Nettoyer tous les callbacks
        this.cleanupCallbacks.forEach(cleanup => cleanup());
        this.cleanupCallbacks = [];
        
        console.log(`[${this.name}] Link cleanup completed`);
    }

    /**
     * Configurer les écouteurs d'événements de Companion
     */
    private setupCompanionListeners(): void {
        if (!this.companionModule || !this.winmediaModule) {
            return;
        }

        // Écouter les actions de Companion
        const companionEventCallback = (event: any) => {
            if (event.type === 'companion_action') {
                this.handleCompanionAction(event.data);
            }
        };

        this.companionModule.onEvent(companionEventCallback);
        this.cleanupCallbacks.push(() => {
            // Note: Dans une implémentation complète, il faudrait pouvoir se désabonner
            // Pour l'instant, on se contente de vider le callback
        });

        console.log(`[${this.name}] Companion event listeners configured`);
    }

    /**
     * Traiter une action reçue de Companion
     */
    private async handleCompanionAction(actionData: any): Promise<void> {
        const { actionType, target } = actionData;

        console.log(`[${this.name}] Processing Companion action: ${actionType}${target ? `(${target})` : ''}`);

        try {
            // Traiter les différents types d'actions WinMedia
            if (actionType.startsWith('winmedia/')) {
                await this.handleWinmediaAction(actionType, target);
            }
        } catch (error) {
            console.error(`[${this.name}] Error processing action ${actionType}:`, error);
        }
    }

    /**
     * Traiter une action WinMedia spécifique
     */
    private async handleWinmediaAction(actionType: string, target?: string): Promise<void> {
        if (!this.winmediaModule) {
            console.error(`[${this.name}] WinMedia module not available`);
            return;
        }

        // Récupérer les commandes configurées
        const commands = this.config.options?.commands || {};

        // Traiter les actions selon le type
        const action = actionType.replace('winmedia/', '');

        if (action === 'custom') {
            if (target) {
                // Le target contient la commande personnalisée à envoyer
                await this.winmediaModule.sendCustomCommand(target);
                console.log(`[${this.name}] WinMedia custom command sent: ${target}`);
            } else {
                console.warn(`[${this.name}] Custom WinMedia command requires target parameter`);
            }
        } else if (commands[action]) {
            // Utiliser la commande configurée
            const command = commands[action].command;
            await this.winmediaModule.sendCustomCommand(command);
            console.log(`[${this.name}] WinMedia ${action} command sent: ${command}`);
        } else {
            // Fallback pour les commandes de base si pas de configuration
            let command: string | null = null;
            switch (action) {
                case 'start':
                    command = '!DIFF-STD2-WM Cartridge-Start 1';
                    break;
                case 'stop':
                    command = '!DIFF-STD2-WM Cartridge-Stop 1';
                    break;
                default:
                    console.warn(`[${this.name}] Unknown WinMedia action: ${actionType}`);
                    return;
            }

            if (command) {
                await this.winmediaModule.sendCustomCommand(command);
                console.log(`[${this.name}] WinMedia ${action} command sent (fallback): ${command}`);
            }
        }
    }
}
