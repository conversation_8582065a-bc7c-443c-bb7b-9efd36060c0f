/**
 * Module Winmedia autonome
 * Responsabilité : Envoi de commandes à Winmedia Server via le fichier winmedia.tmp
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import * as fs from 'fs';
import { BaseModule } from '../core/base/base-module';
import { modulesConfig, winmediaConfig } from '../config';

@Injectable()
export class WinmediaModuleService extends BaseModule implements OnModuleInit, OnModuleDestroy {
    private activated: boolean = false;
    private readonly config = winmediaConfig();

    constructor() {
        super('winmedia', modulesConfig().winmedia.enabled);
        this.log('Winmedia module created', { enabled: this.enabled });
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }
        await new Promise(() => {
            this.activated = true;
        });

        await this.sendCustomCommand('!DIFF-STD2-WM Cartridge-Start 1');
    }

    async stop(): Promise<void> {
        this.log('Stopping Winmedia module...');
        await new Promise(() => {
            this.activated = false;
        });
        this.log('Winmedia module stopped');
    }

    async sendCustomCommand(command: string): Promise<void> {
        if (!this.activated) {
            this.log('Cannot send command - module is not activated');
            return;
        }
        this.log('Sending command:', command);
        await fs.writeFileSync('\\\\' + this.config.network + '\\sesc\\winmedia.tmp', command);
    }
}
