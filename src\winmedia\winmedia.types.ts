/**
 * Types et interfaces pour le module Winmedia
 */

import { IModule } from '../core/interfaces/module.interface';

/**
 * Configuration réseau pour Winmedia Server
 */
export interface WinmediaServerNetworkConfig {
    host: string;
}

/**
 * Configuration complète pour Winmedia
 */
export interface WinmediaConfig {
    network: WinmediaServerNetworkConfig;
}

/**
 * Interface pour le module WinMedia
 * Permet d'envoyer des commandes personnalisées
 */
export interface IWinmediaModule extends IModule {
    /**
     * Envoyer une commande personnalisée à WinMedia Server
     */
    sendCustomCommand(command: string): Promise<void>;
}
