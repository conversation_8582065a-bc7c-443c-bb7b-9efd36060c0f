import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { GabinModuleService } from '../gabin/gabin-module.service';
import { OBSModuleService } from '../obs/obs-module.service';
import { WinmediaModuleService } from '../winmedia/winmedia-module.service';
import { CompanionModuleService } from '../companion/companion-module.service';
import { LinkManagerService } from './services/link-manager.service';

/**
 * Module principal pour les modules autonomes
 * Charge et initialise tous les modules autonomes du Hub
 */
@Module({
    providers: [GabinModuleService, OBSModuleService, WinmediaModuleService, CompanionModuleService, LinkManagerService],
    exports: [GabinModuleService, OBSModuleService, WinmediaModuleService, CompanionModuleService],
})
export class AutonomousModulesModule implements OnModuleInit {
    constructor(
        private readonly linkManager: LinkManagerService,
        private readonly gabinModule: GabinModuleService,
        private readonly obsModule: OBSModuleService,
        private readonly companionModule: CompanionModuleService,
        private readonly winmediaModule: WinmediaModuleService,
    ) {}

    async onModuleInit() {
        console.log('[AutonomousModules] Initializing autonomous modules...');

        // Enregistrer les modules dans le LinkManager
        this.linkManager.registerModule(this.gabinModule);
        this.linkManager.registerModule(this.obsModule);
        this.linkManager.registerModule(this.companionModule);
        this.linkManager.registerModule(this.winmediaModule);

        console.log('[AutonomousModules] Autonomous modules registered');

        // Maintenant initialiser les liens
        await this.linkManager.initializeLinks();

        console.log('[AutonomousModules] Module links initialized');
    }
}
